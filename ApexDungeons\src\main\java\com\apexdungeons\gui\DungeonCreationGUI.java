package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Streamlined dungeon creation interface.
 * Simple 2-click process: Create → Name → Done
 */
public class DungeonCreationGUI {
    private static final Map<UUID, Boolean> awaitingNameInput = new HashMap<>();

    /**
     * Directly prompt for dungeon name (skip intermediate GUI).
     */
    public static void open(Player player, ApexDungeons plugin) {
        // Skip GUI, go directly to name input for maximum simplicity
        promptForName(player, plugin);
    }

    /**
     * Prompt player for dungeon name in chat.
     */
    private static void promptForName(Player player, ApexDungeons plugin) {
        awaitingNameInput.put(player.getUniqueId(), true);

        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "✨ Create New Dungeon " + ChatColor.GRAY + "- Made by Vexy");
        player.sendMessage(ChatColor.YELLOW + "Enter a name for your dungeon:");
        player.sendMessage(ChatColor.GRAY + "• 3-32 characters");
        player.sendMessage(ChatColor.GRAY + "• Letters, numbers, hyphens, underscores only");
        player.sendMessage(ChatColor.GRAY + "• Type 'cancel' to abort");
        player.sendMessage("");

        // Register chat listener
        registerChatListener(plugin);
    }
    
    /**
     * Register chat listener for name input.
     */
    private static void registerChatListener(ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler
            public void onChat(AsyncPlayerChatEvent e) {
                Player player = e.getPlayer();
                if (!awaitingNameInput.containsKey(player.getUniqueId())) return;

                e.setCancelled(true);
                awaitingNameInput.remove(player.getUniqueId());

                String input = e.getMessage().trim();

                if (input.equalsIgnoreCase("cancel")) {
                    player.sendMessage(ChatColor.YELLOW + "Dungeon creation cancelled.");
                    return;
                }

                // Validate name
                if (input.length() < 3 || input.length() > 32) {
                    player.sendMessage(ChatColor.RED + "Name must be 3-32 characters long!");
                    Bukkit.getScheduler().runTask(plugin, () -> promptForName(player, plugin));
                    return;
                }

                if (!input.matches("[a-zA-Z0-9_-]+")) {
                    player.sendMessage(ChatColor.RED + "Name can only contain letters, numbers, hyphens, and underscores!");
                    Bukkit.getScheduler().runTask(plugin, () -> promptForName(player, plugin));
                    return;
                }

                // Check if name already exists
                if (plugin.getDungeonManager().getDungeons().containsKey(input)) {
                    player.sendMessage(ChatColor.RED + "A dungeon with that name already exists!");
                    Bukkit.getScheduler().runTask(plugin, () -> promptForName(player, plugin));
                    return;
                }

                // Create the dungeon immediately
                Bukkit.getScheduler().runTask(plugin, () -> createDungeonInstantly(player, plugin, input));
            }
        }, plugin);
    }
    
    /**
     * Create dungeon instantly with optimized feedback.
     */
    private static void createDungeonInstantly(Player player, ApexDungeons plugin, String name) {
        player.sendMessage(ChatColor.GREEN + "✨ Creating dungeon: " + ChatColor.AQUA + name);
        player.sendMessage(ChatColor.YELLOW + "⏳ Generating optimized flat world...");

        // Validate input
        if (name == null || name.trim().isEmpty()) {
            player.sendMessage(ChatColor.RED + "✗ Invalid dungeon name!");
            return;
        }

        // Check if dungeon already exists
        if (plugin.getDungeonManager().getDungeon(name) != null) {
            player.sendMessage(ChatColor.RED + "✗ A dungeon with that name already exists!");
            return;
        }

        // Use the DungeonManager to create the dungeon
        plugin.getDungeonManager().createDungeon(name, player);

        // Quick success feedback
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage(ChatColor.GREEN + "🎉 Dungeon created successfully!");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.AQUA + "/dgn tp " + name + ChatColor.YELLOW + " to visit your dungeon");
            player.sendMessage(ChatColor.GRAY + "Open Building Tools GUI to start building!");
        }, 20L); // 1 second delay
    }
}
