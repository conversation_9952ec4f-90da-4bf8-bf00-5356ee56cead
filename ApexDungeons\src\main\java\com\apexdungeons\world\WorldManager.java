package com.apexdungeons.world;

import com.apexdungeons.ApexDungeons;
import org.bukkit.*;
import org.bukkit.entity.Player;

import java.io.File;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;

/**
 * Manages isolated dungeon worlds. Creates, loads, and unloads worlds
 * for dungeon instances to ensure complete isolation between dungeons.
 */
public class WorldManager {
    private final ApexDungeons plugin;
    private final Map<String, World> dungeonWorlds = new HashMap<>();
    private final Map<String, String> dungeonToWorld = new HashMap<>();
    private final Set<String> worldsBeingCreated = new HashSet<>(); // Track worlds currently being created

    public WorldManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    /**
     * Create a new isolated world for a dungeon instance.
     * @param dungeonName The internal name of the dungeon
     * @return CompletableFuture that completes with the created world
     */
    public CompletableFuture<World> createDungeonWorld(String dungeonName) {
        CompletableFuture<World> future = new CompletableFuture<>();

        // Check if this dungeon is already being created
        synchronized (worldsBeingCreated) {
            if (worldsBeingCreated.contains(dungeonName)) {
                plugin.getLogger().warning("World creation already in progress for dungeon: " + dungeonName);
                future.complete(null);
                return future;
            }
            worldsBeingCreated.add(dungeonName);
        }

        // Clean up any existing world files first
        cleanupExistingWorldFiles(dungeonName);

        // Add a small delay to prevent rapid-fire world creation conflicts
        // World creation must happen on the main thread
        Bukkit.getScheduler().runTask(plugin, () -> {
            // Generate unique world name and ensure it doesn't exist
            String worldName;
            int attempts = 0;
            do {
                worldName = "dungeon_" + dungeonName + "_" + System.currentTimeMillis() + (attempts > 0 ? "_" + attempts : "");
                attempts++;
            } while (Bukkit.getWorld(worldName) != null && attempts < 10);

            if (Bukkit.getWorld(worldName) != null) {
                plugin.getLogger().severe("Could not generate unique world name after 10 attempts for dungeon: " + dungeonName);
                synchronized (worldsBeingCreated) {
                    worldsBeingCreated.remove(dungeonName);
                }
                future.complete(null);
                return;
            }

            try {
                plugin.getLogger().info("Creating dungeon world: " + worldName);

                // Create world with multiple fallback methods for maximum compatibility
                World world = null;

                // Method 0: Ultra-simple creation first (most likely to succeed)
                try {
                    plugin.getLogger().info("Attempting ultra-simple world creation...");
                    WorldCreator creator0 = new WorldCreator(worldName);
                    creator0.environment(World.Environment.NORMAL);
                    creator0.generateStructures(false);
                    creator0.type(WorldType.FLAT);
                    world = creator0.createWorld();
                    if (world != null) {
                        plugin.getLogger().info("World created successfully with ultra-simple method");
                    }
                } catch (Exception e0) {
                    plugin.getLogger().warning("Ultra-simple method failed: " + e0.getMessage());
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        e0.printStackTrace();
                    }
                }

                if (world == null) {
                    // Method 1: Try with modern superflat generation string
                try {
                    plugin.getLogger().info("Attempting world creation with modern superflat format...");
                    WorldCreator creator = new WorldCreator(worldName);
                    creator.environment(World.Environment.NORMAL);
                    creator.generateStructures(false);
                    creator.type(WorldType.FLAT);
                    creator.generatorSettings("3;minecraft:bedrock,60*minecraft:stone,2*minecraft:dirt,minecraft:grass_block;1;");
                    world = creator.createWorld();
                    if (world != null) {
                        plugin.getLogger().info("World created successfully with modern format");
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Modern format failed: " + e.getMessage());
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        e.printStackTrace();
                    }

                    // Method 2: Try with legacy superflat format
                    try {
                        plugin.getLogger().info("Attempting world creation with legacy superflat format...");
                        WorldCreator creator2 = new WorldCreator(worldName);
                        creator2.environment(World.Environment.NORMAL);
                        creator2.generateStructures(false);
                        creator2.type(WorldType.FLAT);
                        creator2.generatorSettings("2;7,60x1,3x3,2;1;");
                        world = creator2.createWorld();
                        plugin.getLogger().info("World created successfully with legacy format");
                    } catch (Exception e2) {
                        plugin.getLogger().warning("Legacy format failed: " + e2.getMessage());

                        // Method 3: Try with simple flat world (no custom generator)
                        try {
                            plugin.getLogger().info("Attempting world creation with simple flat world...");
                            WorldCreator creator3 = new WorldCreator(worldName);
                            creator3.environment(World.Environment.NORMAL);
                            creator3.generateStructures(false);
                            creator3.type(WorldType.FLAT);
                            // No generator settings - use default flat
                            world = creator3.createWorld();
                            if (world != null) {
                                plugin.getLogger().info("World created successfully with simple flat format");
                            }
                        } catch (Exception e3) {
                            plugin.getLogger().warning("Simple flat failed: " + e3.getMessage());
                            if (plugin.getConfig().getBoolean("debug", false)) {
                                e3.printStackTrace();
                            }

                            // Method 4: Last resort - create normal world
                            try {
                                plugin.getLogger().info("Attempting world creation with normal world type...");
                                WorldCreator creator4 = new WorldCreator(worldName);
                                creator4.environment(World.Environment.NORMAL);
                                creator4.generateStructures(false);
                                // Use normal world type as absolute fallback
                                world = creator4.createWorld();
                                plugin.getLogger().info("World created successfully with normal world type");
                            } catch (Exception e4) {
                                plugin.getLogger().severe("Method 4 failed: " + e4.getMessage());

                                // Method 5: Ultra-simple world creation
                                try {
                                    plugin.getLogger().info("Attempting ultra-simple world creation...");
                                    WorldCreator creator5 = new WorldCreator(worldName);
                                    world = creator5.createWorld();
                                    plugin.getLogger().info("World created successfully with ultra-simple method");
                                } catch (Exception e5) {
                                    plugin.getLogger().severe("All world creation methods failed: " + e5.getMessage());
                                    e5.printStackTrace();
                                }
                            }
                        }
                    }
                }

                if (world == null) {
                    plugin.getLogger().severe("CRITICAL: All world creation methods failed for dungeon: " + dungeonName);
                    plugin.getLogger().severe("This may be due to:");
                    plugin.getLogger().severe("1. Insufficient server permissions");
                    plugin.getLogger().severe("2. Disk space issues");
                    plugin.getLogger().severe("3. Bukkit/Spigot version compatibility");
                    plugin.getLogger().severe("4. World name conflicts");
                    plugin.getLogger().severe("5. Server configuration issues");
                    plugin.getLogger().severe("6. Plugin conflicts affecting world creation");

                    // Additional debugging information
                    plugin.getLogger().info("Attempted world name: " + worldName);
                    plugin.getLogger().info("Server version: " + Bukkit.getVersion());
                    plugin.getLogger().info("Available worlds: " + Bukkit.getWorlds().stream().map(World::getName).toList());

                    // Remove from creation tracking on failure
                    synchronized (worldsBeingCreated) {
                        worldsBeingCreated.remove(dungeonName);
                    }

                    future.complete(null);
                    return;
                }

                // Verify world was created properly and is accessible
                plugin.getLogger().info("World created successfully: " + world.getName());
                plugin.getLogger().info("World generator: " + world.getGenerator());
                plugin.getLogger().info("World environment: " + world.getEnvironment());

                // Verify world is actually accessible through Bukkit
                World verifyWorld = Bukkit.getWorld(world.getName());
                if (verifyWorld == null) {
                    plugin.getLogger().severe("CRITICAL: World created but not accessible through Bukkit!");
                    plugin.getLogger().severe("World name: " + world.getName());

                    // Remove from creation tracking on verification failure
                    synchronized (worldsBeingCreated) {
                        worldsBeingCreated.remove(dungeonName);
                    }

                    future.complete(null);
                    return;
                }

                // Quick world configuration (minimal operations for speed)
                configureWorldQuick(world);

                // Store mappings
                dungeonWorlds.put(worldName, world);
                dungeonToWorld.put(dungeonName, worldName);

                // Final verification that mappings work
                World testRetrieve = getDungeonWorld(dungeonName);
                if (testRetrieve == null) {
                    plugin.getLogger().severe("CRITICAL: World mapping verification failed!");
                    plugin.getLogger().severe("Dungeon: " + dungeonName + ", World: " + worldName);
                    // Clean up failed mappings
                    dungeonWorlds.remove(worldName);
                    dungeonToWorld.remove(dungeonName);

                    // Remove from creation tracking on mapping failure
                    synchronized (worldsBeingCreated) {
                        worldsBeingCreated.remove(dungeonName);
                    }

                    future.complete(null);
                    return;
                }

                plugin.getLogger().info("Created and verified isolated world '" + worldName + "' for dungeon '" + dungeonName + "'");
                plugin.getLogger().info("World mapping verified successfully");

                // Remove from creation tracking
                synchronized (worldsBeingCreated) {
                    worldsBeingCreated.remove(dungeonName);
                }

                future.complete(world);
                }

            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error creating world for dungeon " + dungeonName, e);

                // Remove from creation tracking on error
                synchronized (worldsBeingCreated) {
                    worldsBeingCreated.remove(dungeonName);
                }

                future.complete(null);
            }
        });

        return future;
    }

    /**
     * Clean up any existing world files that might cause conflicts.
     */
    private void cleanupExistingWorldFiles(String dungeonName) {
        try {
            // Look for any existing world directories that might conflict
            File worldContainer = Bukkit.getWorldContainer();
            File[] existingDirs = worldContainer.listFiles((dir, name) ->
                name.startsWith("dungeon_" + dungeonName + "_"));

            if (existingDirs != null && existingDirs.length > 0) {
                plugin.getLogger().info("Found " + existingDirs.length + " existing world directories for dungeon: " + dungeonName);
                for (File dir : existingDirs) {
                    plugin.getLogger().info("Cleaning up old world directory: " + dir.getName());
                    deleteWorldFolder(dir);
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error during world cleanup: " + e.getMessage());
        }
    }

    /**
     * Quick world configuration for optimal performance.
     */
    private void configureWorldQuick(World world) {
        // Disable natural spawning
        world.setSpawnFlags(false, false);
        
        // Set game rules for dungeon environment
        world.setGameRule(GameRule.DO_DAYLIGHT_CYCLE, false);
        world.setGameRule(GameRule.DO_WEATHER_CYCLE, false);
        world.setGameRule(GameRule.DO_MOB_SPAWNING, false);
        world.setGameRule(GameRule.KEEP_INVENTORY, true);
        world.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, false);
        world.setGameRule(GameRule.DO_FIRE_TICK, false);
        world.setGameRule(GameRule.MOB_GRIEFING, false);

        // Aggressive anti-structure protection
        world.setGameRule(GameRule.DO_TRADER_SPAWNING, false);
        world.setGameRule(GameRule.DO_PATROL_SPAWNING, false);
        world.setGameRule(GameRule.DO_WARDEN_SPAWNING, false);

        plugin.getLogger().info("World configured with anti-structure protection: " + world.getName());

        // Set time and weather
        world.setTime(18000); // Night time for dungeon atmosphere
        world.setStorm(false);
        world.setThundering(false);
        
        // Set spawn location to the actual grass surface in superflat world
        // Our custom superflat: bedrock(0) + stone(1-60) + dirt(61-62) + grass(63) = surface at Y=64
        world.setSpawnLocation(0, 64, 0);

        // Quick spawn setup (no expensive block checks)
        plugin.getLogger().info("World configured quickly: " + world.getName());
    }





    /**
     * Get the world associated with a dungeon.
     */
    public World getDungeonWorld(String dungeonName) {
        String worldName = dungeonToWorld.get(dungeonName);
        if (worldName != null) {
            World world = dungeonWorlds.get(worldName);
            if (world == null) {
                // Try to get world from Bukkit in case it wasn't cached
                world = Bukkit.getWorld(worldName);
                if (world != null) {
                    dungeonWorlds.put(worldName, world);
                    plugin.getLogger().info("Re-cached world: " + worldName);
                }
            }
            return world;
        }
        plugin.getLogger().warning("No world mapping found for dungeon: " + dungeonName);
        plugin.getLogger().info("Available dungeon mappings: " + dungeonToWorld.keySet());
        return null;
    }



    /**
     * Unload and delete a dungeon world.
     */
    public CompletableFuture<Boolean> deleteDungeonWorld(String dungeonName) {
        return CompletableFuture.supplyAsync(() -> {
            String worldName = dungeonToWorld.get(dungeonName);
            if (worldName == null) {
                return false;
            }

            World world = dungeonWorlds.get(worldName);
            if (world == null) {
                return false;
            }

            try {
                // Teleport all players out of the world
                Location mainWorldSpawn = Bukkit.getWorlds().get(0).getSpawnLocation();
                for (Player player : world.getPlayers()) {
                    player.teleport(mainWorldSpawn);
                    player.sendMessage(ChatColor.YELLOW + "You have been teleported out of a dungeon world that is being deleted.");
                }

                // Unload the world
                boolean unloaded = Bukkit.unloadWorld(world, false);
                if (!unloaded) {
                    plugin.getLogger().warning("Failed to unload world: " + worldName);
                    return false;
                }

                // Delete world files
                File worldFolder = new File(Bukkit.getWorldContainer(), worldName);
                if (worldFolder.exists()) {
                    deleteWorldFolder(worldFolder);
                }

                // Remove from mappings
                dungeonWorlds.remove(worldName);
                dungeonToWorld.remove(dungeonName);

                plugin.getLogger().info("Deleted dungeon world: " + worldName);
                return true;

            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error deleting world for dungeon " + dungeonName, e);
                return false;
            }
        });
    }

    /**
     * Recursively delete a world folder.
     */
    private void deleteWorldFolder(File folder) {
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteWorldFolder(file);
                }
            }
        }
        folder.delete();
    }

    /**
     * Get spawn location for a dungeon world.
     */
    public Location getDungeonSpawnLocation(String dungeonName) {
        // First check for custom spawn location
        Location customSpawn = plugin.getDungeonConfig().getCustomSpawnLocation(dungeonName);
        if (customSpawn != null) {
            plugin.getLogger().info("Using custom spawn location for " + dungeonName + ": " +
                customSpawn.getBlockX() + "," + customSpawn.getBlockY() + "," + customSpawn.getBlockZ());
            return customSpawn;
        }

        // Use default logic
        World world = getDungeonWorld(dungeonName);
        if (world != null) {
            // Try to get the dungeon instance to find the origin
            var dungeon = plugin.getDungeonManager().getDungeon(dungeonName);
            if (dungeon != null && dungeon.getOrigin() != null) {
                // Use the dungeon's origin location but find the actual surface
                Location origin = dungeon.getOrigin().clone();
                Location surfaceLocation = findSafeSpawnLocation(world, origin.getBlockX(), origin.getBlockZ());

                plugin.getLogger().info("Found spawn location for " + dungeonName + " at origin: " +
                    surfaceLocation.getBlockX() + "," + surfaceLocation.getBlockY() + "," + surfaceLocation.getBlockZ());

                return surfaceLocation;
            } else {
                // Fallback to world spawn location at surface level
                Location surfaceLocation = findSafeSpawnLocation(world, 0, 0);

                plugin.getLogger().info("Using fallback spawn location for dungeon '" + dungeonName + "': " + surfaceLocation);
                return surfaceLocation;
            }
        }
        plugin.getLogger().warning("Could not find spawn location for dungeon: " + dungeonName);
        plugin.getLogger().info("Available dungeons: " + dungeonToWorld.keySet());
        return null;
    }

    /**
     * Find a safe spawn location on the surface at the given coordinates.
     */
    private Location findSafeSpawnLocation(World world, int x, int z) {
        // For our custom superflat worlds, we know the structure:
        // Y=0: Bedrock, Y=1-60: Stone, Y=61-62: Dirt, Y=63: Grass
        // So surface level should be Y=64 (on top of grass block at Y=63)

        // Ensure the chunk is loaded first
        world.getChunkAt(x >> 4, z >> 4);

        // First try the known superflat surface level
        Location loc = new Location(world, x + 0.5, 64, z + 0.5); // Y=64 to stand on grass at Y=63

        // Verify this is actually a safe location by checking blocks
        if (world.getBlockAt(x, 63, z).getType().isSolid() &&
            world.getBlockAt(x, 64, z).getType().isAir() &&
            world.getBlockAt(x, 65, z).getType().isAir()) {
            return loc;
        }

        // If not safe, fix the blocks
        world.getBlockAt(x, 63, z).setType(Material.GRASS_BLOCK);
        world.getBlockAt(x, 64, z).setType(Material.AIR);
        world.getBlockAt(x, 65, z).setType(Material.AIR);

        return loc;
    }

    /**
     * Check if a world is a dungeon world.
     */
    public boolean isDungeonWorld(World world) {
        return dungeonWorlds.containsValue(world);
    }

    /**
     * Get dungeon name from world.
     */
    public String getDungeonNameFromWorld(World world) {
        for (Map.Entry<String, String> entry : dungeonToWorld.entrySet()) {
            if (dungeonWorlds.get(entry.getValue()) == world) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * Register a dungeon world mapping.
     */
    public void registerDungeonWorld(String dungeonName, World world) {
        dungeonToWorld.put(dungeonName, world.getName());
        dungeonWorlds.put(world.getName(), world);
        plugin.getLogger().info("Registered dungeon world mapping: " + dungeonName + " -> " + world.getName());
    }

    /**
     * Get dungeon to world mappings for debugging.
     */
    public Map<String, String> getDungeonToWorldMappings() {
        return new HashMap<>(dungeonToWorld);
    }

    /**
     * Shutdown and cleanup all dungeon worlds.
     */
    public void shutdown() {
        plugin.getLogger().info("Cleaning up " + dungeonWorlds.size() + " dungeon worlds...");
        
        for (String dungeonName : new HashMap<>(dungeonToWorld).keySet()) {
            deleteDungeonWorld(dungeonName).join();
        }
        
        dungeonWorlds.clear();
        dungeonToWorld.clear();
    }


}
